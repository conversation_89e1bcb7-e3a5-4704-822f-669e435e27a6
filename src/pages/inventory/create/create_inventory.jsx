import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import {
  useGetAllProductdetailListQuery,
} from "../../../feature/api/productDataApiSlice";
import { useNavigate } from "react-router-dom";
import { useState, useRef } from "react";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useCreateInventoryMutation } from "../../../feature/api/inventoryDataApiSlice";
import WebLoader from "../../../components/webLoader";
import { useSelector } from "react-redux";

const getInitialValues = () => {
  const baseValues = {
    branch_id: "",
    product_id: "",
    original_price:  "",
    selling_price:  "",
    stock_quantity: "",
    minimum_alert_stock_quantity: "",
    stock_status: "",
  };

  return baseValues;
};
const getValidationSchema = () => {
  const baseValidation = {
    branch_id: yup.string().required().label("Branch"),
    product_id: yup.string().required().label("Product"),
    original_price: yup.number().min(0).required().label("Original Price"),
    selling_price: yup.number().min(0).required().label("Selling Price"),
    stock_quantity: yup.number().min(0).required().label("Stock Quantity"),
    stock_status: yup.string().required().label("Stock Status"),
    minimum_alert_stock_quantity: yup.number().min(0).required().label("Minimum Alert Stock Quantity"),
  };

  return yup.object().shape(baseValidation);
};

const stockStatusList = [
  { value: 1,
    label: "In Stock",
  },
  {
    value: 2,
    label: "Out of Stock"
  }
];

export default function CreateInventory() {
  const navigate = useNavigate(); // Initialize the navigation hook
  const activePage = "Add Inventory";
  const linkHref = "/dashboard";

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + ' (' + values.branch_type_name + ')',
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector((state) => state.commonState.generalStatus);
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** Start list General Status ******************* */

  const [selectedProductDetailId, setSelectedProductDetailId] = useState("");
  const [selectedProductId, setSelectedProductId] = useState("");
  const [selectedBranchId, setSelectedBranchId] = useState("");

  // Track current form values to preserve them during reinitialization
  const currentFormValuesRef = useRef({});

  /* **************** Start Fetch Product Details (Load In Select Box) ******************* */

  const handleBranchChange = (e) => {
    setSelectedBranchId(e.target.value);
  };

  const { data: productListResp } = useGetAllProductdetailListQuery({
    branch_id: parseInt(selectedBranchId),
    skip: !selectedBranchId,
  });

  const productList = productListResp?.data || [];
  const productAry = productList.map((product) => ({
    value: product.id,
    label: product.product_name + '(barcode: '  +  product.bar_code + ' - sku: ' + product.sku + ')',
    product_key: product.product_id
  }));
  /* **************** End Fetch Product Details (Load In Select Box) ******************* */

  /* **************** Start Edit Inventory ******************* */
  const [handleCreateInventoryApi, {isLoading: isLoading}] = useCreateInventoryMutation();
  const handleSubmit = async (body) => {
    try {

      const attributes = [];
      Object.keys(body).forEach(key => {
        if (key.startsWith('attribute_') && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const inventoryData = {
        branch_id: parseInt(selectedBranchId),
        product_detail_id: parseInt(selectedProductDetailId),
        product_id: parseInt(selectedProductId),
        stock_quantity: parseInt(body.stock_quantity),
        minimum_alert_stock_quantity: parseInt(body.minimum_alert_stock_quantity),
        stock_status: parseInt(body.stock_status),
        original_price: body.original_price,
        selling_price: body.selling_price,
        is_publish: parseInt(body.is_publish)
      };

      const resp = await handleCreateInventoryApi(inventoryData).unwrap();
      handleApiSuccess(resp);
      navigate("/inventory");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Inventory ******************* */

  const handleProductSelection = (e) => {
    setSelectedProductDetailId(e.target.value);
      const selectedProduct = productAry.find(
        (product) => parseInt(product.value) === parseInt(e.target.value)
      );
      if (selectedProduct) {
        setSelectedProductId(selectedProduct.product_key);
      }
    };

  /* **************** Web Loader  ******************* */
    if (isLoading)
      return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                    >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Add Inventory</h4>
                            <p className="card-subtitle mb-4">
                              To add Inventory, fill details and save from here
                            </p>
                            <Formik
                              initialValues={getInitialValues()}
                              enableReinitialize={true}
                              validationSchema={getValidationSchema()}
                              onSubmit={handleSubmit}
                            >
                              {(formikProps) => {
                                currentFormValuesRef.current = formikProps.values;                                
                                return (
                              <Form
                                name="product-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="branch_id" className="form-label">
                                            Branch
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="branch_id"
                                            id="branch_id"
                                            className="form-select"
                                            type="select"
                                            options={branchesList}
                                            onChange={handleBranchChange}
                                          />
                                        </div>
                                  </div> 
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="product_id"
                                        className="form-label"
                                      >
                                        Product
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="product_id"
                                        id="product_id"
                                        className="form-select"
                                        type="select"
                                        options={productAry}
                                        onChange={handleProductSelection}
                                      />
                                    </div>
                                  </div>                                                   
                                  {selectedProductId && selectedProductId !== "" && (
                                    <>
                                    <hr></hr>
                                    <h4 className="card-title">Inventory Details</h4>
                                                                                                              
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="original_price" className="form-label">
                                            Original Price
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="original_price"
                                            id="original_price"
                                            placeholder="Enter orignal price *"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="selling_price" className="form-label">
                                            Selling Price
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="selling_price"
                                            id="selling_price"
                                            placeholder="Enter selling price *"
                                            className="form-control"
                                            step="0.01"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="stock_quantity" className="form-label">
                                            Stock Quantity
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="stock_quantity"
                                            id="stock_quantity"
                                            placeholder="Enter stock quantity *"
                                            className="form-control"
                                            min="0"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="minimum_alert_stock_quantity" className="form-label">
                                            Minimum Alert Stock Quantity
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="minimum_alert_stock_quantity"
                                            id="minimum_alert_stock_quantity"
                                            placeholder="Enter minimum alert stock quantity *"
                                            className="form-control"
                                            min="0"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label htmlFor="stock_status" className="form-label">
                                            Stock Status
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            type="select"
                                            name="stock_status"
                                            id="stock_status"
                                            className="form-select"
                                            options={stockStatusList}
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="is_publish" className="form-label">
                                          Status
                                          <span className="un-validation">(*)</span> :
                                        </label>
                                        <FormikField
                                          name="is_publish"
                                          id="is_publish"
                                          className="form-select"
                                          type="select"
                                          options={generalStatusList}
                                        />
                                      </div>
                                    </div>
                                    </>
                                  )}
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Add Inventory
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                                );
                              }}
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
