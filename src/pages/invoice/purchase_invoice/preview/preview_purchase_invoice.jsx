import CommonHeader from "../../../../components/layout/common_header";
import CommonFooter from "../../../../components/layout/common_footer";
import TopBar from "../../../../components/layout/topBar";
import Breadcrumb from "../../../../components/breadcrumb";
import { useNavigate, useParams } from "react-router-dom";
import { handleToast } from "../../../../hooks/handleToast";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { useEffect, useMemo, useRef, useState } from "react";
import WebLoader from "../../../../components/webLoader";
import { handleCustomError } from "../../../../hooks/handleCustomError";
import {
  useListSupplierInvoiceHistoryQuery,
  useSingleSupplierInvoiceQuery,
} from "../../../../feature/api/supplierInvoiceDataApiSlice";
import { Table } from "../../../../components/datatable";
import { useSelector } from "react-redux";
import { handleApiErrors } from "../../../../hooks/handleApiErrors";
import { useListAllSupplierQuery } from "../../../../feature/api/supplierDataApiSlice";
import { useGetShopBranchsQuery } from "../../../../feature/api/branchDataApiSlice";
import { PaginationComponent } from "../../../../components/pagination";
import { Modal } from "react-bootstrap";

export default function PreviewPurchaseInvoice() {
  const navigation = useNavigate();
  const { id } = useParams();
  const invoiceId = parseInt(id);
  const activePage = "Preview Payment To Supplier Invoice";
  const linkHref = "/dashboard";
  const previewRef = useRef(null);
  const [isPDFSaving, setIsPDFSaving] = useState(false);

  /* *************** Start listing invoice history ************ */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterSupplier, setFilterSupplier] = useState(null);
  const [filterBranchId, setFilterBranchId] = useState(null);
  const [filterInvoiceType, setFilterInvoiceType] = useState(null);
  const [filterInvoiceStatus, setFilterInvoiceStatus] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");
  const [filterDate, setFilterDate] = useState("");
  const { data: supplierInvoiceHistoryListResp, isLoading: isHistoryLoading } =
    useListSupplierInvoiceHistoryQuery({
      invoice_id: invoiceId,
      page: currentPage,
      supplier_id: parseInt(filterSupplier),
      branch_id: parseInt(filterBranchId),
      invoice_type: parseInt(filterInvoiceType),
      status: parseInt(filterInvoiceStatus),
      date: filterDate,
      keywords: filterKeywords,
    });
  const paymentToSupplierInvoiceList = useMemo(() => {
    if (!supplierInvoiceHistoryListResp?.data?.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return supplierInvoiceHistoryListResp?.data?.list;
  }, [currentPage, supplierInvoiceHistoryListResp?.data?.list]);
  const pageData = useMemo(
    () => supplierInvoiceHistoryListResp?.data?.page ?? null,
    [supplierInvoiceHistoryListResp]
  );

  /* ****************  Start Filter ****************** */
  const handleSupplierFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterSupplier(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleBranchFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterBranchId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleInvoiceTypeFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterInvoiceType(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleInvoiceStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterInvoiceStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleDateFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterDate(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start list all invoice types ******************* */
  const invoiceTypes = useSelector((state) => state.commonState.invoiceTypes);
  /* **************** End list all invoice types ******************* */

  /* **************** Start list all invoice statuses ******************* */
  const invoiceStatusData = useSelector(
    (state) => state.commonState.invoiceStatus
  );
  const invoiceStatusList = useMemo(() => {
    if (!invoiceStatusData?.data?.length) {
      return [];
    }
    return invoiceStatusData.data.map((values) => ({
      value: values.id,
      label: values.status,
    }));
  }, [invoiceStatusData?.data]);
  /* **************** Start list all invoice statuses ******************* */

  /* **************** Start list suppliers ******************* */
  const userSupplierData = useListAllSupplierQuery();
  const userSupplierDataList = userSupplierData?.data?.data || [];
  const userSupplierList = userSupplierDataList.map((values) => ({
    value: values.id,
    label: values.supplier_name + " (" + values.supplier_code + ")",
  }));
  /* **************** End list suppliers ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** End Filter ***************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  /* *************** End listing invoice history ************** */

  /* **************** Start fetching single invoice data ******************* */
  let { data: singleSupplier, isLoading: isInvoiceLoading } =
    useSingleSupplierInvoiceQuery({
      id: invoiceId,
    });

  const invoiceData = useMemo(() => {
    return singleSupplier?.data || null;
  }, [singleSupplier]);
  /* **************** Start fetching single invoice data ******************* */

  const [initialValues, setFormValues] = useState({
    id: invoiceData?.id ? invoiceData?.id : "",
    branch_id: invoiceData?.branch_id ? invoiceData?.branch_id : "",
    date: invoiceData?.date,
    time: invoiceData?.time,
    supplier_id: invoiceData?.supplier_id ? invoiceData?.supplier_id : "",
    supplier_invoice_number: invoiceData?.supplier_invoice_number
      ? invoiceData?.supplier_invoice_number
      : "",
    currency: invoiceData?.from_currency_code
      ? invoiceData?.from_currency_code
      : "",
    items: invoiceData?.items
      ? invoiceData?.items
      : [
          {
            id: "",
            description: "",
            unit_price: "",
            quantity: "",
            total_price: "",
            total_price_in_shop_currency: "",
          },
        ],
    exchange_rate: invoiceData?.exchange_rate ? invoiceData?.exchange_rate : "",
    sub_total: invoiceData?.sub_total_from ? invoiceData?.sub_total_from : "",
    sub_total_in_shop_currency: invoiceData?.sub_total_to
      ? invoiceData.sub_total_to
      : "",
    shipping_currency:
      invoiceData?.shipping_charge_from_currency_same == 1
        ? invoiceData?.shipping_charge_to_currency_code
        : invoiceData?.shipping_charge_from_currency_code,
    shipping_charge: invoiceData?.shipping_charge_from
      ? invoiceData?.shipping_charge_from
      : "",
    shipping_charge_in_shop_currency: invoiceData?.shipping_charge_to
      ? invoiceData?.shipping_charge_to
      : "",
    invoice_type: invoiceData?.invoice_type ? invoiceData?.invoice_type : "",
    total_amount: invoiceData?.total_from ? invoiceData?.total_from : "",
    total_amount_in_shop_currency: invoiceData?.total_to
      ? invoiceData?.total_to
      : "",
    transaction_fee_currency:
      invoiceData?.transaction_fee_from_currency_same == 1
        ? invoiceData?.transaction_fee_to_currency_code
        : invoiceData?.transaction_fee_from_currency_code,
    transaction_fee: invoiceData?.transaction_fee_from
      ? invoiceData?.transaction_fee_from
      : "",
    transaction_fee_in_shop_currency: invoiceData?.transaction_fee_to
      ? invoiceData?.transaction_fee_to
      : "",
    other_expense_currency:
      invoiceData?.other_expense_charges_from_currency_same == 1
        ? invoiceData?.other_expense_charges_to_currency_code
        : invoiceData?.other_expense_charges_from_currency_code,
    other_expense: invoiceData?.other_expense_charges_from
      ? invoiceData?.other_expense_charges_from
      : "",
    other_expense_in_shop_currency: invoiceData?.other_expense_charges_to
      ? invoiceData?.other_expense_charges_to
      : "",
  });

  useEffect(() => {
    if (isInvoiceLoading) return;

    if (!invoiceData) {
      handleCustomError("Error was found, please try again later!");
      navigation("/paymentToSupplierInvoice");
    } else {
      setFormValues({
        id: invoiceData?.id ? invoiceData?.id : "",
        branch_id: invoiceData?.branch_id ? invoiceData?.branch_id : "",
        date: invoiceData?.date,
        time: invoiceData?.time,
        supplier_id: invoiceData?.supplier_id ? invoiceData?.supplier_id : "",
        supplier_invoice_number: invoiceData?.supplier_invoice_number
          ? invoiceData?.supplier_invoice_number
          : "",
        currency: invoiceData?.from_currency_code
          ? invoiceData?.from_currency_code
          : "",
        items: invoiceData?.items
          ? invoiceData?.items
          : [
              {
                id: "",
                description: "",
                unit_price: "",
                quantity: "",
                total_price: "",
                total_price_in_shop_currency: "",
              },
            ],
        exchange_rate: invoiceData?.exchange_rate
          ? invoiceData?.exchange_rate
          : "",
        sub_total: invoiceData?.sub_total_from
          ? invoiceData?.sub_total_from
          : "",
        sub_total_in_shop_currency: invoiceData?.sub_total_to
          ? invoiceData.sub_total_to
          : "",
        shipping_currency:
          invoiceData?.shipping_charge_from_currency_same == 1
            ? invoiceData?.shipping_charge_to_currency_code
            : invoiceData?.shipping_charge_from_currency_code,
        shipping_charge: invoiceData?.shipping_charge_from
          ? invoiceData?.shipping_charge_from
          : "",
        shipping_charge_in_shop_currency: invoiceData?.shipping_charge_to
          ? invoiceData?.shipping_charge_to
          : "",
        invoice_type: invoiceData?.invoice_type
          ? invoiceData?.invoice_type
          : "",
        total_amount: invoiceData?.total_from ? invoiceData?.total_from : "",
        total_amount_in_shop_currency: invoiceData?.total_to
          ? invoiceData?.total_to
          : "",
        transaction_fee_currency:
          invoiceData?.transaction_fee_from_currency_same == 1
            ? invoiceData?.transaction_fee_to_currency_code
            : invoiceData?.transaction_fee_from_currency_code,
        transaction_fee: invoiceData?.transaction_fee_from
          ? invoiceData?.transaction_fee_from
          : "",
        transaction_fee_in_shop_currency: invoiceData?.transaction_fee_to
          ? invoiceData?.transaction_fee_to
          : "",
        other_expense_currency:
          invoiceData?.other_expense_charges_from_currency_same == 1
            ? invoiceData?.other_expense_charges_to_currency_code
            : invoiceData?.other_expense_charges_from_currency_code,
        other_expense: invoiceData?.other_expense_charges_from
          ? invoiceData?.other_expense_charges_from
          : "",
        other_expense_in_shop_currency: invoiceData?.other_expense_charges_to
          ? invoiceData?.other_expense_charges_to
          : "",
      });
    }
  }, [isInvoiceLoading, invoiceData, navigation]);

  const handleDownloadPDF = () => {
    setIsPDFSaving(true);
    if (previewRef.current) {
      html2canvas(previewRef.current, {
        scale: 2,
        useCORS: true,
        logging: false,
      })
        .then((canvas) => {
          const imgData = canvas.toDataURL("image/png");
          const pdf = new jsPDF({
            orientation: "portrait",
            unit: "mm",
            format: "a4",
          });

          const imgWidth = 190;
          const pageHeight = 297;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          let heightLeft = imgHeight;
          let position = 10;

          pdf.addImage(imgData, "PNG", 10, position, imgWidth, imgHeight);
          heightLeft -= pageHeight - 20;

          while (heightLeft >= 0) {
            position = heightLeft - imgHeight + 10;
            pdf.addPage();
            pdf.addImage(imgData, "PNG", 10, position, imgWidth, imgHeight);
            heightLeft -= pageHeight - 20;
          }

          // save the PDF
          pdf.save(`invoice-${invoiceData?.id || "preview"}.pdf`);

          // Generate a Blob from the PDF
          const pdfBlob = pdf.output("blob");
          const pdfUrl = URL.createObjectURL(pdfBlob);

          // Open the PDF in a new tab
          const newTab = window.open(pdfUrl, "_blank");
          if (!newTab) {
            console.error("Failed to open new tab.");
            handleToast(
              "error",
              "Failed to open PDF in new tab. Please allow popups."
            );
          }

          // Clean up the URL object after a short delay to ensure the tab opens
          setTimeout(() => URL.revokeObjectURL(pdfUrl), 1000);

          setIsPDFSaving(false);
        })
        .catch((error) => {
          console.error("Error generating PDF:", error);
          handleToast("error", "Failed to generate PDF. Please try again.");
          setIsPDFSaving(false);
        });
    }
  };

  /* ******* Start Preview Modal ********** */

  /* **************** Data Edit Model ******************* */
  const [showInvoiceHistoryModal, setShowInvoiceHistoryModal] = useState(false);
  const handleInvoiceHistoryModalClose = () =>
    setShowInvoiceHistoryModal(false);
  const handleInvoiceHistoryModalShow = () => setShowInvoiceHistoryModal(true);
  /* **************** End Data Edit Model ******************* */

  const [invoiceHistoryModalValues, setInvoiceHistoryModalValues] = useState({
    id: "",
    branch_id: "",
    date: "",
    time: "",
    supplier_id: "",
    supplier_invoice_number: "",
    currency: "",
    items:  [
          {
            id: "",
            description: "",
            unit_price: "",
            quantity: "",
            total_price: "",
            total_price_in_shop_currency: "",
          },
        ],
    exchange_rate: "",
    sub_total: "",
    sub_total_in_shop_currency: "",
    shipping_currency: "",
    shipping_charge: "",
    shipping_charge_in_shop_currency: "",
    invoice_type: "",
    total_amount: "",
    total_amount_in_shop_currency: "",
    transaction_fee_currency: "",
    transaction_fee: "",
    transaction_fee_in_shop_currency: "",
    other_expense_currency: "",
    other_expense: "",
    other_expense_in_shop_currency: "",
    user_name: "",
  });

  const handleOpenModal = (values) => {
    setInvoiceHistoryModalValues(values);
    handleInvoiceHistoryModalShow();
  };

  const invoicePreviewHandler = (d) => {
    handleOpenModal({
      id: d?.id ? d?.id : "",
      branch_id: d?.branch_id ? d?.branch_id : "",
      branch_name: d?.branch_name,
      date: d?.date,
      time: d?.time,
      supplier_id: d?.supplier_id ? d?.supplier_id : "",
      supplier_name: d?.supplier_name,
      supplier_invoice_number: d?.supplier_invoice_number
        ? d?.supplier_invoice_number
        : "",
      from_currency_code: d?.from_currency_code ? d?.from_currency_code : "",
      toCurrency_code: d?.toCurrency_code ? d?.toCurrency_code : "",
      items: d?.items
        ? d?.items
        : [
            {
              id: "",
              description: "",
              unit_price: "",
              quantity: "",
              total_price: "",
              total_price_in_shop_currency: "",
            },
          ],
      exchange_rate_with_currency: d?.exchange_rate_with_currency ? d?.exchange_rate_with_currency : "",
      sub_total_from: d?.sub_total_from ? d?.sub_total_from : "",
      sub_total_to: d?.sub_total_to ? d.sub_total_to : "",
      shipping_charge_from_currency_code: d?.shipping_charge_from_currency_code ? d?.shipping_charge_from_currency_code : "",
      shipping_charge_to_currency_code: d?.shipping_charge_to_currency_code ? d?.shipping_charge_to_currency_code : "",
      shipping_charge_from: d?.shipping_charge_from ? d?.shipping_charge_from : 0,
      shipping_charge_to: d?.shipping_charge_to
        ? d?.shipping_charge_to
        : 0,
      invoice_type_name: d?.invoice_type_name,
      invoice_type: d?.invoice_type ? d?.invoice_type : "",
      total_from_with_currency: d?.total_from_with_currency ? d?.total_from_with_currency : "",
      total_to_with_currency: d?.total_to_with_currency ? d?.total_to_with_currency : "",
      transaction_fee_from_currency_code: d?.transaction_fee_from_currency_code ? d?.transaction_fee_from_currency_code : "",
      transaction_fee_to_currency_code: d?.transaction_fee_to_currency_code ? d?.transaction_fee_to_currency_code : "",
      transaction_fee_to: d?.transaction_fee_to
        ? d?.transaction_fee_to
        : 0,
      transaction_fee_from: d?.transaction_fee_from
      ? d?.transaction_fee_from
      : 0,
      other_expense_charges_from_currency_code: d?.other_expense_charges_from_currency_code || "" ,
      other_expense_charges_to_currency_code: d?.other_expense_charges_to_currency_code || "" ,
      other_expense_charges_from: d?.other_expense_charges_from
        ? d?.other_expense_charges_from
        : 0,
      other_expense_charges_to: d?.other_expense_charges_to
        ? d?.other_expense_charges_to
        : 0,
       user_name: d?.user_name || "" ,
    });
  };

  /* ******* End Preview Modal ********** */

  /* **************** Web Loader  ******************* */
  if (isPDFSaving || isHistoryLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container" style={{ padding: "30px" }}>
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="col-12 download-button-div">
                            <div className="d-flex align-items-center justify-content-end mt-2 me-2 gap-6">
                              <div className="btn-group">
                                <button
                                  className="btn btn-primary dropdown-toggle"
                                  type="button"
                                  id="dropdownMenuButton"
                                  data-bs-toggle="dropdown"
                                  aria-expanded="false"
                                >
                                  PRINT
                                </button>
                                <ul
                                  className="dropdown-menu"
                                  aria-labelledby="dropdownMenuButton"
                                >
                                  <li>
                                    <a
                                      className="dropdown-item"
                                      type="submit"
                                      onClick={handleDownloadPDF}
                                      href="javascript:void(0)"
                                    >
                                      A4 Format
                                    </a>
                                  </li>
                                  {/* <li>
                                  <a className="dropdown-item" type="submit" onClick={handleDownloadPDF} href="javascript:void(0)">Thermal Print</a>
                                </li> */}
                                </ul>
                              </div>
                              <button
                                className="btn btn-primary"
                                type="submit"
                                onClick={handleDownloadPDF}
                              >
                                DOWNLOAD (PDF)
                              </button>
                            </div>
                          </div>
                          <div className="card-body p-4" ref={previewRef}>
                            {/* <h4 className="card-title">
                              Preview Payment To Supplier Invoice
                            </h4> */}
                            <>
                              <div className="row">
                                <div className="col-lg-6"></div>
                              </div>
                              <div className="row">
                                <div className="col-lg-4">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="branch_id"
                                      className="form-label"
                                    >
                                      Branch
                                    </label>
                                    <p>{invoiceData?.branch_name}</p>
                                  </div>
                                </div>
                                <div className="col-lg-2"></div>
                                <div className="col-lg-2">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="date"
                                      className="form-label"
                                    >
                                      Date
                                    </label>
                                    <p>{invoiceData?.date}</p>
                                  </div>
                                </div>
                                <div className="col-lg-2">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="time"
                                      className="form-label"
                                    >
                                      Time
                                    </label>
                                    <p>{invoiceData?.time}</p>
                                  </div>
                                </div>
                              </div>
                              <div className="row">
                                <div className="col-lg-4">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="supplier_id"
                                      className="form-label"
                                    >
                                      Supplier
                                    </label>
                                    <p>{invoiceData?.supplier_name}</p>
                                  </div>
                                </div>
                                <div className="col-lg-2"></div>
                                <div className="col-lg-2">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="currency"
                                      className="form-label"
                                    >
                                      Currency
                                    </label>
                                    <p>{invoiceData?.from_currency_code}</p>
                                  </div>
                                </div>
                                <div className="col-lg-2">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="exchange_rate"
                                      className="form-label"
                                    >
                                      Exchange Rate
                                    </label>
                                    <p>
                                      {invoiceData?.exchange_rate_with_currency}
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div className="row">
                                <div className="col-lg-4">
                                  <div className="mb-3">
                                    <label
                                      htmlFor="invoice_type"
                                      className="form-label"
                                    >
                                      Invoice Type
                                    </label>
                                    <p>{invoiceData?.invoice_type_name}</p>
                                  </div>
                                </div>
                              </div>
                              <>
                                <hr></hr>
                                <h4 className="card-title">Item Details</h4>
                                <div className="row">
                                  <div className="card-body">
                                    <div className="row ">
                                      <div className="row">
                                        <div className="col-3">
                                          <label
                                            htmlFor=""
                                            className="form-label"
                                          >
                                            Item Description
                                          </label>
                                        </div>
                                        <div className="col-2 align-start">
                                          <label
                                            htmlFor=""
                                            className="form-label"
                                          >
                                            Unit Price (
                                            {invoiceData?.from_currency_code}){" "}
                                          </label>
                                        </div>
                                        <div className="col-1 align-center">
                                          <label
                                            htmlFor=""
                                            className="form-label"
                                          >
                                            Qty {""}
                                          </label>
                                        </div>
                                        <div className="col-2 align-start">
                                          <label
                                            htmlFor=""
                                            className="form-label "
                                          >
                                            Total Price (
                                            {invoiceData?.from_currency_code})
                                          </label>
                                        </div>
                                        <div className="col-2 align-center">
                                          <label
                                            htmlFor=""
                                            className="form-label"
                                          >
                                            Total Price (
                                            {invoiceData?.toCurrency_code})
                                          </label>
                                        </div>
                                        <div className="col-3"></div>
                                      </div>
                                      <hr></hr>
                                      <div id="fields" className="my-4">
                                        {invoiceData?.items.map(
                                          (item, index) => (
                                            <div className="row" key={index}>
                                              <div className="col-3">
                                                <div className="mb-3">
                                                  <p>
                                                    {item.description || "N/A"}
                                                  </p>
                                                </div>
                                              </div>
                                              <div className="col-2">
                                                <div className="mb-3">
                                                  <p>
                                                    {invoiceData?.from_currency_code +
                                                      " " +
                                                      item.unit_price || "N/A"}
                                                  </p>
                                                </div>
                                              </div>
                                              <div className="col-1">
                                                <div className="mb-3">
                                                  <p>
                                                    {item.quantity || "N/A"}
                                                  </p>
                                                </div>
                                              </div>
                                              <div className="col-2">
                                                <div className="mb-3">
                                                  <p>
                                                    {invoiceData?.from_currency_code +
                                                      " " +
                                                      item.total_price || "N/A"}
                                                  </p>
                                                </div>
                                              </div>
                                              <div className="col-2">
                                                <div className="mb-3">
                                                  <p>
                                                    {invoiceData?.toCurrency_code +
                                                      " " +
                                                      item.total_price_in_shop_currency ||
                                                      "N/A"}
                                                  </p>
                                                </div>
                                              </div>
                                            </div>
                                          )
                                        )}
                                      </div>
                                      <hr></hr>
                                      <div className="d-flex justify-content-end">
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="sub_total"
                                              className="form-label"
                                            >
                                              Sub Total :
                                            </label>
                                          </div>
                                        </div>
                                        <div className="col-lg-1">
                                          <div className="mb-3">
                                            <p>
                                              {invoiceData?.from_currency_code}{" "}
                                              {invoiceData?.sub_total_from}
                                            </p>
                                            <p>
                                              {invoiceData?.toCurrency_code}{" "}
                                              {invoiceData?.sub_total_to}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="d-flex justify-content-end">
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="sub_total"
                                              className="form-label"
                                            >
                                              Shipping Charge :
                                            </label>
                                          </div>
                                        </div>
                                        <div className="col-lg-1">
                                          <div className="mb-3">
                                            <p>
                                              {
                                                invoiceData?.shipping_charge_from_currency_code
                                              }{" "}
                                              {
                                                invoiceData?.shipping_charge_from
                                              }
                                            </p>
                                            <p>
                                              {
                                                invoiceData?.shipping_charge_to_currency_code
                                              }{" "}
                                              {invoiceData?.shipping_charge_to}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="d-flex justify-content-end">
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="sub_total"
                                              className="form-label"
                                            >
                                              Transaction Fee :
                                            </label>
                                          </div>
                                        </div>
                                        <div className="col-lg-1">
                                          <div className="mb-3">
                                            <p>
                                              {
                                                invoiceData?.transaction_fee_from_currency_code
                                              }{" "}
                                              {
                                                invoiceData?.transaction_fee_from
                                              }
                                            </p>
                                            <p>
                                              {
                                                invoiceData?.transaction_fee_to_currency_code
                                              }{" "}
                                              {invoiceData?.transaction_fee_to}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="d-flex justify-content-end">
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="sub_total"
                                              className="form-label"
                                            >
                                              Other Expense :
                                            </label>
                                          </div>
                                        </div>
                                        <div className="col-lg-1">
                                          <div className="mb-3">
                                            <p>
                                              {
                                                invoiceData?.other_expense_charges_from_currency_code
                                              }{" "}
                                              {
                                                invoiceData?.other_expense_charges_from
                                              }
                                            </p>
                                            <p>
                                              {
                                                invoiceData?.other_expense_charges_to_currency_code
                                              }{" "}
                                              {
                                                invoiceData?.other_expense_charges_to
                                              }
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                      <hr></hr>
                                      <div className="d-flex justify-content-end">
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="sub_total"
                                              className="form-label"
                                            >
                                              Total Amount :
                                            </label>
                                          </div>
                                        </div>
                                        <div className="col-lg-1">
                                          <div className="mb-3">
                                            <p>
                                              {
                                                invoiceData?.total_from_with_currency
                                              }
                                            </p>
                                            <p>
                                              {
                                                invoiceData?.total_to_with_currency
                                              }
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </>
                            </>
                            </div>
                            <div className="table-responsive">
                              <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                <div className="d-flex gap-6">
                                  <div>
                                    <select
                                      value={filterBranchId}
                                      className="form-control search-chat py-2"
                                      onChange={handleBranchFilter}
                                    >
                                      <option value="">All Branches</option>
                                      {branchesList.map((option) => (
                                        <option
                                          key={option.value}
                                          value={option.value}
                                        >
                                          {option.label}
                                        </option>
                                      ))}
                                    </select>
                                  </div>
                                  <div>
                                    <select
                                      value={filterSupplier}
                                      className="form-control search-chat py-2"
                                      onChange={handleSupplierFilter}
                                    >
                                      <option value="">All Suppliers</option>
                                      {userSupplierList.map((option) => (
                                        <option
                                          key={option.value}
                                          value={option.value}
                                        >
                                          {option.label}
                                        </option>
                                      ))}
                                    </select>
                                  </div>
                                  <div>
                                    <select
                                      value={filterInvoiceType}
                                      className="form-control search-chat py-2"
                                      onChange={handleInvoiceTypeFilter}
                                    >
                                      <option value="">
                                        All Invoice Types
                                      </option>
                                      {invoiceTypes?.data?.map((option) => (
                                        <option
                                          key={option.invoice_type_id}
                                          value={option.invoice_type_id}
                                        >
                                          {option.invoice_type}
                                        </option>
                                      ))}
                                    </select>
                                  </div>
                                  <div>
                                    <select
                                      value={filterInvoiceStatus}
                                      className="form-control search-chat py-2"
                                      onChange={handleInvoiceStatusFilter}
                                    >
                                      <option value="">
                                        All Invoice Status
                                      </option>
                                      {invoiceStatusList.map((option) => (
                                        <option
                                          key={option.value}
                                          value={option.value}
                                        >
                                          {option.label}
                                        </option>
                                      ))}
                                    </select>
                                  </div>
                                  <div>
                                    <input
                                      type="date"
                                      className="form-control search-chat py-2"
                                      onChange={handleDateFilter}
                                    />
                                  </div>
                                </div>
                                <div className="position-relative">
                                  <input
                                    type="text"
                                    className="form-control search-chat py-2 ps-5"
                                    id="text-srh"
                                    onChange={handleKeywordsFilter}
                                    placeholder="Keyword Search..."
                                    value={filterKeywords}
                                  />
                                  <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                </div>
                              </div>
                              <Table
                                headCells={[
                                  { key: "sel_id", label: "#", align: "left" },
                                  {
                                    key: "supplier_invoice_number",
                                    label: "Invoice no.",
                                    align: "left",
                                  },
                                  {
                                    key: "supplier_name",
                                    label: "Supplier Name",
                                    align: "left",
                                    linkTo: (row) => ({
                                      to: `/viewSupplier/${row.supplier_id}`,
                                    }),
                                  },
                                  {
                                    key: "invoice_type_name",
                                    label: "Invoice Type",
                                    align: "left",
                                  },
                                  {
                                    key: "branch_name",
                                    label: "Branch",
                                    align: "left",
                                    linkTo: (row) => ({
                                      to: `/editBranch/${row.branch_id}`,
                                    }),
                                  },
                                  {
                                    key: "exchange_rate_with_currency",
                                    label: "Exchange Rate",
                                    align: "center",
                                  },
                                  {
                                    key: "total_from_with_currency",
                                    label: "Total From",
                                    align: "center",
                                  },
                                  {
                                    key: "total_to_with_currency",
                                    label: "Total To",
                                    align: "center",
                                  },
                                  {
                                    key: "user_name",
                                    label: "User",
                                    align: "left",
                                    linkTo: (row) => ({
                                      to: `/userProfile/${row.user_id}`,
                                    }),
                                  },
                                  {
                                    key: "status_name",
                                    key_id: "status",
                                    label: "Status",
                                    align: "center",
                                  },
                                ]}
                                data={paymentToSupplierInvoiceList}
                                customActions={[
                                  {
                                    label: "Preview",
                                    icon: "ti ti-eye",
                                    handler: invoicePreviewHandler,
                                  },
                                ]}
                              />
                              <PaginationComponent
                                totalCount={pageData?.total_count}
                                pageSize={pageData?.page_size}
                                currentPage={currentPage}
                                setCurrentPage={setCurrentPage}
                                onPageChange={fetchData}
                              />
                            </div>
                          {/* </div> */}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <!-- Invoice History Modal --> */}

      <Modal
        show={showInvoiceHistoryModal}
        onHide={handleInvoiceHistoryModalClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
        size="xl"
      >
        <Modal.Header closeButton>
          <Modal.Title>Supplier Invoice History</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="modal-body">
            <div className="row">
              <>
                <div className="row">
                  <div className="col-lg-6"></div>
                </div>
                <div className="row">
                  <div className="col-lg-4">
                    <div className="mb-3">
                      <label htmlFor="branch_id" className="form-label">
                        Branch
                      </label>
                      <p>{invoiceHistoryModalValues?.branch_name}</p>
                    </div>
                  </div>
                  <div className="col-lg-2"></div>
                  <div className="col-lg-2">
                    <div className="mb-3">
                      <label htmlFor="date" className="form-label">
                        Date
                      </label>
                      <p>{invoiceHistoryModalValues?.date}</p>
                    </div>
                  </div>
                  <div className="col-lg-2">
                    <div className="mb-3">
                      <label htmlFor="time" className="form-label">
                        Time
                      </label>
                      <p>{invoiceHistoryModalValues?.time}</p>
                    </div>
                  </div>
                </div>
                <div className="row">
                  <div className="col-lg-4">
                    <div className="mb-3">
                      <label htmlFor="supplier_id" className="form-label">
                        Supplier
                      </label>
                      <p>{invoiceHistoryModalValues?.supplier_name}</p>
                    </div>
                  </div>
                  <div className="col-lg-2"></div>
                  <div className="col-lg-2">
                    <div className="mb-3">
                      <label htmlFor="currency" className="form-label">
                        Currency
                      </label>
                      <p>{invoiceHistoryModalValues?.from_currency_code}</p>
                    </div>
                  </div>
                  <div className="col-lg-2">
                    <div className="mb-3">
                      <label htmlFor="exchange_rate" className="form-label">
                        Exchange Rate
                      </label>
                      <p>{invoiceHistoryModalValues?.exchange_rate_with_currency}</p>
                    </div>
                  </div>
                </div>
                <div className="row">
                  <div className="col-lg-4">
                    <div className="mb-3">
                      <label htmlFor="invoice_type" className="form-label">
                        Invoice Type
                      </label>
                      <p>{invoiceHistoryModalValues?.invoice_type_name}</p>
                    </div>
                  </div>
                </div>
                <>
                  <hr></hr>
                  <h4 className="card-title">Item Details</h4>
                  <div className="row">
                    <div className="card-body">
                      <div className="row ">
                        <div className="row">
                          <div className="col-3">
                            <label htmlFor="" className="form-label">
                              Item Description
                            </label>
                          </div>
                          <div className="col-2 align-start">
                            <label htmlFor="" className="form-label">
                              Unit Price ({invoiceHistoryModalValues?.from_currency_code}){" "}
                            </label>
                          </div>
                          <div className="col-1 align-center">
                            <label htmlFor="" className="form-label">
                              Qty {""}
                            </label>
                          </div>
                          <div className="col-2 align-start">
                            <label htmlFor="" className="form-label ">
                              Total Price ({invoiceHistoryModalValues?.from_currency_code})
                            </label>
                          </div>
                          <div className="col-2 align-center">
                            <label htmlFor="" className="form-label">
                              Total Price ({invoiceHistoryModalValues?.toCurrency_code})
                            </label>
                          </div>
                          <div className="col-3"></div>
                        </div>
                        <hr></hr>
                        <div id="fields" className="my-4">
                          {invoiceHistoryModalValues?.items.map((item, index) => (
                            <div className="row" key={index}>
                              <div className="col-3">
                                <div className="mb-3">
                                  <p>{item.description || "N/A"}</p>
                                </div>
                              </div>
                              <div className="col-2">
                                <div className="mb-3">
                                  <p>
                                    {invoiceHistoryModalValues?.from_currency_code +
                                      " " +
                                      item.unit_price || "N/A"}
                                  </p>
                                </div>
                              </div>
                              <div className="col-1">
                                <div className="mb-3">
                                  <p>{item.quantity || "N/A"}</p>
                                </div>
                              </div>
                              <div className="col-2">
                                <div className="mb-3">
                                  <p>
                                    {invoiceHistoryModalValues?.from_currency_code +
                                      " " +
                                      item.total_price || "N/A"}
                                  </p>
                                </div>
                              </div>
                              <div className="col-2">
                                <div className="mb-3">
                                  <p>
                                    {invoiceHistoryModalValues?.toCurrency_code +
                                      " " +
                                      item.total_price_in_shop_currency ||
                                      "N/A"}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        <hr></hr>
                        <div className="d-flex justify-content-end">
                          <div className="col-lg-2">
                            <div className="mb-3">
                              <label htmlFor="sub_total" className="form-label">
                                Sub Total :
                              </label>
                            </div>
                          </div>
                          <div className="col-lg-1">
                            <div className="mb-3">
                              <p>
                                {invoiceHistoryModalValues?.from_currency_code}{" "}
                                {invoiceHistoryModalValues?.sub_total_from}
                              </p>
                              <p>
                                {invoiceHistoryModalValues?.toCurrency_code}{" "}
                                {invoiceHistoryModalValues?.sub_total_to}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="d-flex justify-content-end">
                          <div className="col-lg-2">
                            <div className="mb-3">
                              <label htmlFor="sub_total" className="form-label">
                                Shipping Charge :
                              </label>
                            </div>
                          </div>
                          <div className="col-lg-1">
                            <div className="mb-3">
                              <p>
                                {
                                  invoiceHistoryModalValues?.shipping_charge_from_currency_code
                                }{" "}
                                {invoiceHistoryModalValues?.shipping_charge_from}
                              </p>
                              <p>
                                {invoiceHistoryModalValues?.shipping_charge_to_currency_code}{" "}
                                {invoiceHistoryModalValues?.shipping_charge_to}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="d-flex justify-content-end">
                          <div className="col-lg-2">
                            <div className="mb-3">
                              <label htmlFor="sub_total" className="form-label">
                                Transaction Fee :
                              </label>
                            </div>
                          </div>
                          <div className="col-lg-1">
                            <div className="mb-3">
                              <p>
                                {
                                  invoiceHistoryModalValues?.transaction_fee_from_currency_code
                                }{" "}
                                {invoiceHistoryModalValues?.transaction_fee_from}
                              </p>
                              <p>
                                {invoiceHistoryModalValues?.transaction_fee_to_currency_code}{" "}
                                {invoiceHistoryModalValues?.transaction_fee_to}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="d-flex justify-content-end">
                          <div className="col-lg-2">
                            <div className="mb-3">
                              <label htmlFor="sub_total" className="form-label">
                                Other Expense :
                              </label>
                            </div>
                          </div>
                          <div className="col-lg-1">
                            <div className="mb-3">
                              <p>
                                {
                                  invoiceHistoryModalValues?.other_expense_charges_from_currency_code
                                }{" "}
                                {invoiceHistoryModalValues?.other_expense_charges_from}
                              </p>
                              <p>
                                {
                                  invoiceHistoryModalValues?.other_expense_charges_to_currency_code
                                }{" "}
                                {invoiceHistoryModalValues?.other_expense_charges_to}
                              </p>
                            </div>
                          </div>
                        </div>
                        <hr></hr>
                        <div className="d-flex justify-content-end">                      
                          <div className="col-lg-2">
                            <div className="mb-3">
                              <label htmlFor="sub_total" className="form-label">
                                Created By 
                              </label>
                              <p>{invoiceHistoryModalValues?.user_name}</p>
                            </div>
                          </div>
                          <div className="col-lg-7"></div>
                          <div className="col-lg-2">
                            <div className="mb-3">
                              <label htmlFor="sub_total" className="form-label">
                                Total Amount :
                              </label>
                            </div>
                          </div>
                          <div className="col-lg-1">
                            <div className="mb-3">
                              <p>{invoiceHistoryModalValues?.total_from_with_currency}</p>
                              <p>{invoiceHistoryModalValues?.total_to_with_currency}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              </>
            </div>
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
              data-bs-dismiss="modal"
              onClick={handleInvoiceHistoryModalClose}
            >
              Close
            </button>
          </div>
        </Modal.Body>
        {/* <Modal.Footer></Modal.Footer> */}
      </Modal>
      {/* <!-- Invoice History Modal end --> */}

      <CommonFooter />
    </>
  );
}
