import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider } from "react-router-dom";

import { LoginPage } from "../pages/login";
import { DashboardPage } from "../pages/dashboard";
import ProtectedRoute from "./protectedRoute";
import AuthRoute from "./authRoutes";
import { Account } from "../pages/account_settings";
import { ShopProfle } from "../pages/shop_profile";
import { BranchList } from "../pages/branch";
import { CreateBranch } from "../pages/branch/create";
import { EditBranch } from "../pages/branch/update";
import { RoleList } from "../pages/roles";
import { StaffList } from "../pages/staffs";
import { CreateStaff } from "../pages/staffs/create";
import EditStaff from "../pages/staffs/update/edit_staff";
import { Categories } from "../pages/categories";
import { SubCategories } from "../pages/subcategories";
import { ChildCategories } from "../pages/childcategories";
import { Attributes } from "../pages/attributes";
import { AttributesValues } from "../pages/attributes_values";
import { Brands } from "../pages/brands";
import { ProductList } from "../pages/products";
import { CreateProduct } from "../pages/products/create";
import { EditProduct } from "../pages/products/update";
import { Inventory } from "../pages/inventory";
import { CreateInventory } from "../pages/inventory/create";
import { EditInventory } from "../pages/inventory/update";
import InventoryHistory from "../pages/inventory/history/inventory_history";
import { CustomerList } from "../pages/customers";
import { CreateCustomer } from "../pages/customers/create";
import { EditCustomer } from "../pages/customers/update";
import { CustomerContacts } from "../pages/customer_contacts";
import CustomerNotes from "../pages/customer_notes/customer_notes";
import { CustomerAttachments } from "../pages/customer_attachments";
import ViewCustomer from "../pages/customers/view/view_customer";
import CreateSupplier from "../pages/suppliers/create/create_supplier";
import SupplierList from "../pages/suppliers/supplier_list";
import { EditSupplier } from "../pages/suppliers/update";
import ViewSupplier from "../pages/suppliers/view/view_supplier";
import { OutOfStockInventory } from "../pages/inventory/out_of_stock_inventory/out_of_stock_inventory";
import OutOfStockQuantityInventory from "../pages/inventory/out_of_stock_quantity_inventory/out_of_stock_quantity_inventory";
import MinimumStockAlertQuantityInventory from "../pages/inventory/minimum_stock_alert_quantity_inventory/minimum_stock_alert_quantity_inventory";
import { PreviewSupplierInvoice } from "../pages/invoice/supplier_invoice/preview";
import { ViewUser } from "../pages/user_profile/view";
import { PurchaseInvoice } from "../pages/invoice/purchase_invoice";
import { CreatePurchaseInvoice } from "../pages/invoice/purchase_invoice/create";
import { EditPurchaseInvoice } from "../pages/invoice/purchase_invoice/edit";
import PreviewPurchaseInvoice from "../pages/invoice/purchase_invoice/preview/preview_purchase_invoice";
import { SupplierInvoice } from "../pages/invoice/supplier_invoice";
import { CreateSupplierInvoice } from "../pages/invoice/supplier_invoice/create";
import { EditSupplierInvoice } from "../pages/invoice/supplier_invoice/edit";

const router = createBrowserRouter([
  {
    path: "/",
    //element: <DashboardLayout />,
    children: [
      {
        index: true,
        element: (
          <AuthRoute>
            <LoginPage />
          </AuthRoute>
        ),
      },
      {
        path: "/login",
        element: (
          <AuthRoute>
            <LoginPage />
          </AuthRoute>
        ),
      },
      {
        path: "/dashboard",
        element: (
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        ),
      },
      {
        path: "/account",
        element: (
          <ProtectedRoute>
            <Account />
          </ProtectedRoute>
        ),
      },
      {
        path: "/userProfile/:id",
        element: (
          <ProtectedRoute>
            <ViewUser />
          </ProtectedRoute>
        ),
      },
      {
        path: "/shop_profile",
        element: (
          <ProtectedRoute>
            <ShopProfle />
          </ProtectedRoute>
        ),
      },
      {
        path: "/branch_list",
        element: (
          <ProtectedRoute>
            <BranchList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createBranch",
        element: (
          <ProtectedRoute>
            <CreateBranch />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editBranch/:id",
        element: (
          <ProtectedRoute>
            <EditBranch />
          </ProtectedRoute>
        ),
      },
      {
        path: "/roles",
        element: (
          <ProtectedRoute>
            <RoleList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/staffs",
        element: (
          <ProtectedRoute>
            <StaffList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createStaff",
        element: (
          <ProtectedRoute>
            <CreateStaff />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editStaff/:id",
        element: (
          <ProtectedRoute>
            <EditStaff />
          </ProtectedRoute>
        ),
      },
      {
        path: "/mainCategories",
        element: (
          <ProtectedRoute>
            <Categories />
          </ProtectedRoute>
        ),
      },
      {
        path: "/subCategories",
        element: (
          <ProtectedRoute>
            <SubCategories />
          </ProtectedRoute>
        ),
      },
      {
        path: "/childCategories",
        element: (
          <ProtectedRoute>
            <ChildCategories />
          </ProtectedRoute>
        ),
      },
      {
        path: "/attributes",
        element: (
          <ProtectedRoute>
            <Attributes />
          </ProtectedRoute>
        ),
      },
      {
        path: "/attributesValues",
        element: (
          <ProtectedRoute>
            <AttributesValues />
          </ProtectedRoute>
        ),
      },
      {
        path: "/brands",
        element: (
          <ProtectedRoute>
            <Brands />
          </ProtectedRoute>
        ),
      },
      {
        path: "/products",
        element: (
          <ProtectedRoute>
            <ProductList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createProduct",
        element: (
          <ProtectedRoute>
            <CreateProduct />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editProduct/:id",
        element: (
          <ProtectedRoute>
            <EditProduct />
          </ProtectedRoute>
        ),
      },
      {
        path: "/inventory",
        element: (
          <ProtectedRoute>
            <Inventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/outOfStockInventory",
        element: (
          <ProtectedRoute>
            <OutOfStockInventory />
          </ProtectedRoute>
        ),
      },{
        path: "/outOfStockQuantityInventory",
        element: (
          <ProtectedRoute>
            <OutOfStockQuantityInventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/minimumStockAlertQuantityInventory",
        element: (
          <ProtectedRoute>
            <MinimumStockAlertQuantityInventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createInventory",
        element: (
          <ProtectedRoute>
            <CreateInventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editInventory/:id",
        element: (
          <ProtectedRoute>
            <EditInventory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/inventoryHistory",
        element: (
          <ProtectedRoute>
            <InventoryHistory />
          </ProtectedRoute>
        ),
      },
      {
        path: "/customers",
        element: (
          <ProtectedRoute>
            <CustomerList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createCustomer",
        element: (
          <ProtectedRoute>
            <CreateCustomer />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editCustomer/:id",
        element: (
          <ProtectedRoute>
            <EditCustomer />
          </ProtectedRoute>
        ),
      },
      {
        path: "/customerContacts",
        element: (
          <ProtectedRoute>
            <CustomerContacts />
          </ProtectedRoute>
        ),
      },
      {
        path: "/customerNotes",
        element: (
          <ProtectedRoute>
            <CustomerNotes />
          </ProtectedRoute>
        ),
      },
      {
        path: "/customerAttachments",
        element: (
          <ProtectedRoute>
            <CustomerAttachments />
          </ProtectedRoute>
        ),
      },
      {
        path: "/viewCustomer/:id",
        element: (
          <ProtectedRoute>
            <ViewCustomer />
          </ProtectedRoute>
        ),
      },
      {
        path: "/suppliers",
        element: (
          <ProtectedRoute>
            <SupplierList />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createSupplier",
        element: (
          <ProtectedRoute>
            <CreateSupplier />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editSupplier/:id",
        element: (
          <ProtectedRoute>
            <EditSupplier />
          </ProtectedRoute>
        ),
      },
      {
        path: "/viewSupplier/:id",
        element: (
          <ProtectedRoute>
            <ViewSupplier />
          </ProtectedRoute>
        ),
      },
      {
        path: "/supplierInvoice",
        element: (
          <ProtectedRoute>
            <SupplierInvoice />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createSupplierInvoice",
        element: (
          <ProtectedRoute>
            <CreateSupplierInvoice />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editSupplierInvoice/:id",
        element: (
          <ProtectedRoute>
            <EditSupplierInvoice />
          </ProtectedRoute>
        ),
      },
      {
        path: "/previewSupplierInvoice/:id",
        element: (
          <ProtectedRoute>
            <PreviewSupplierInvoice />
          </ProtectedRoute>
        ),
      },
       {
        path: "/purchaseInvoice",
        element: (
          <ProtectedRoute>
            <PurchaseInvoice />
          </ProtectedRoute>
        ),
      },
      {
        path: "/createPurchaseInvoice",
        element: (
          <ProtectedRoute>
            <CreatePurchaseInvoice />
          </ProtectedRoute>
        ),
      },
      {
        path: "/editPurchaseInvoice/:id",
        element: (
          <ProtectedRoute>
            <EditPurchaseInvoice />
          </ProtectedRoute>
        ),
      },
      {
        path: "/previewPurchaseInvoice/:id",
        element: (
          <ProtectedRoute>
            <PreviewPurchaseInvoice />
          </ProtectedRoute>
        ),
      },
    ],
  },
  {
    path: "*",
    element: <div> Not found! </div>,
  },
]);

export default function Router() {
  return <RouterProvider router={router} />;
}
